import React, { useState } from 'react';
import { generateTweet, predictEngagement } from '../services/apiService';

const TwitterGenerator = ({ onClose, onUseTweet }) => {
  const [keywords, setKeywords] = useState('');
  const [tone, setTone] = useState('professional');
  const [length, setLength] = useState('medium');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');

  const handleGenerate = async () => {
    if (!keywords.trim()) {
      setError('请输入关键词');
      return;
    }

    setIsLoading(true);
    setError('');
    
    try {
      // Generate multiple tweet options
      const tweetPromises = Array(3).fill().map(() => 
        generateTweet({
          keywords: keywords.trim(),
          tone,
          length
        })
      );

      const tweets = await Promise.all(tweetPromises);
      
      // Get engagement predictions for each tweet
      const resultsWithPredictions = await Promise.all(
        tweets.map(async (tweet) => {
          const prediction = await predictEngagement(tweet);
          return {
            text: tweet,
            prediction
          };
        })
      );

      setResults(resultsWithPredictions);
    } catch (err) {
      setError('生成推文时出错，请稍后重试');
      console.error('Generation error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUseTweet = (tweetText) => {
    onUseTweet(tweetText);
    onClose();
  };

  return (
    <div className="generator-modal-content">
      <div className="generator-modal-header">
        <h2 className="generator-modal-title">AI推文生成器</h2>
        <button className="generator-close-btn" onClick={onClose}>
          ×
        </button>
      </div>

      <form className="generator-form" onSubmit={(e) => { e.preventDefault(); handleGenerate(); }}>
        <div className="generator-input-group">
          <label className="generator-label" htmlFor="keywords">
            关键词 *
          </label>
          <input
            id="keywords"
            type="text"
            className="generator-input"
            placeholder="输入要讨论的主题或关键词..."
            value={keywords}
            onChange={(e) => setKeywords(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <div className="generator-input-group">
          <label className="generator-label" htmlFor="tone">
            语调风格
          </label>
          <select
            id="tone"
            className="generator-input"
            value={tone}
            onChange={(e) => setTone(e.target.value)}
            disabled={isLoading}
          >
            <option value="professional">专业</option>
            <option value="casual">轻松</option>
            <option value="humorous">幽默</option>
            <option value="inspirational">励志</option>
            <option value="controversial">争议</option>
          </select>
        </div>

        <div className="generator-input-group">
          <label className="generator-label" htmlFor="length">
            推文长度
          </label>
          <select
            id="length"
            className="generator-input"
            value={length}
            onChange={(e) => setLength(e.target.value)}
            disabled={isLoading}
          >
            <option value="short">简短 (50-100字)</option>
            <option value="medium">中等 (100-200字)</option>
            <option value="long">较长 (200-280字)</option>
          </select>
        </div>

        {error && (
          <div style={{ color: '#e0245e', fontSize: '14px', marginTop: '8px' }}>
            {error}
          </div>
        )}

        <button
          type="submit"
          className="generator-generate-btn"
          disabled={isLoading || !keywords.trim()}
        >
          {isLoading ? '生成中...' : '生成推文'}
        </button>
      </form>

      {isLoading && (
        <div className="generator-loading">
          <div className="generator-spinner"></div>
          <span>正在生成推文...</span>
        </div>
      )}

      {results.length > 0 && (
        <div className="generator-results">
          <h3 style={{ marginBottom: '16px', fontSize: '18px', fontWeight: '600' }}>
            生成结果
          </h3>
          {results.map((result, index) => (
            <div key={index} className="generator-result-item">
              <div className="generator-tweet-text">
                {result.text}
              </div>
              
              <div className="generator-engagement-prediction">
                <div className="generator-metric">
                  <span>👍</span>
                  <span className="generator-metric-value">
                    {result.prediction.likes}
                  </span>
                </div>
                <div className="generator-metric">
                  <span>🔄</span>
                  <span className="generator-metric-value">
                    {result.prediction.retweets}
                  </span>
                </div>
                <div className="generator-metric">
                  <span>💬</span>
                  <span className="generator-metric-value">
                    {result.prediction.replies}
                  </span>
                </div>
                <div className="generator-metric">
                  <span>📊</span>
                  <span className="generator-metric-value">
                    {result.prediction.engagementRate}%
                  </span>
                </div>
              </div>

              <button
                className="generator-use-btn"
                onClick={() => handleUseTweet(result.text)}
              >
                使用这条推文
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TwitterGenerator;
