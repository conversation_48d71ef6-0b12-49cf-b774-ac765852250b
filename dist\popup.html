<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Twitter Content Generator</title><style>body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
        }
        
        .popup-container {
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #1da1f2, #0d8bd9);
            border-radius: 12px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 700;
            color: #14171a;
            margin: 0;
        }
        
        .subtitle {
            font-size: 14px;
            color: #657786;
            margin: 5px 0 0 0;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: #f7f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #17bf63;
        }
        
        .status-text {
            font-size: 14px;
            color: #14171a;
            font-weight: 500;
        }
        
        .quick-actions {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #14171a;
            margin-bottom: 12px;
        }
        
        .action-button {
            width: 100%;
            padding: 12px 16px;
            background: white;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #14171a;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 8px;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .action-button:hover {
            border-color: #1da1f2;
            background: #f7f9fa;
        }
        
        .action-button:last-child {
            margin-bottom: 0;
        }
        
        .action-icon {
            font-size: 16px;
        }
        
        .settings-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e8ed;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .setting-label {
            font-size: 14px;
            color: #14171a;
        }
        
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #ccd6dd;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .toggle-switch.active {
            background: #1da1f2;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s ease;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(20px);
        }
        
        .footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e1e8ed;
            text-align: center;
        }
        
        .footer-text {
            font-size: 12px;
            color: #657786;
        }
        
        .footer-link {
            color: #1da1f2;
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }</style><script defer="defer" src="popup.js"></script></head><body><div class="popup-container"><div class="header"><div class="logo">🤖</div><h1 class="title">Twitter Content Generator</h1><p class="subtitle">AI驱动的推文生成工具</p></div><div class="status"><div class="status-indicator"></div><span class="status-text">扩展已激活</span></div><div class="quick-actions"><h3 class="section-title">快速操作</h3><button class="action-button" id="openTwitter"><span class="action-icon">🐦</span> <span>打开 Twitter</span></button> <button class="action-button" id="viewHelp"><span class="action-icon">❓</span> <span>使用帮助</span></button> <button class="action-button" id="viewStats"><span class="action-icon">📊</span> <span>使用统计</span></button></div><div class="settings-section"><h3 class="section-title">设置</h3><div class="setting-item"><span class="setting-label">启用扩展</span><div class="toggle-switch active" id="enableToggle"><div class="toggle-slider"></div></div></div><div class="setting-item"><span class="setting-label">自动检测</span><div class="toggle-switch active" id="autoDetectToggle"><div class="toggle-slider"></div></div></div></div><div class="footer"><p class="footer-text">版本 1.0.0 | <a href="#" class="footer-link" id="feedbackLink">反馈建议</a></p></div></div><script src="popup.js"></script></body></html>