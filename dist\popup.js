(()=>{function e(e,n){const o=!n.classList.contains("active");o?n.classList.add("active"):n.classList.remove("active");const i={};i[e]=o,chrome.storage.sync.set(i,function(){chrome.runtime.lastError?(console.error("Error saving setting:",chrome.runtime.lastError),o?n.classList.remove("active"):n.classList.add("active")):(console.log("Setting saved:",e,o),"enabled"===e&&t(o))})}function t(e){const t=document.querySelector(".status-indicator"),n=document.querySelector(".status-text");e?(t.style.background="#17bf63",n.textContent="扩展已激活"):(t.style.background="#f45d22",n.textContent="扩展已禁用")}function n(e){const t=document.getElementById("popup-modal");t&&t.remove();const n=document.createElement("div");n.id="popup-modal",n.style.cssText="\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: rgba(0, 0, 0, 0.5);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: 10000;\n  ";const o=document.createElement("div");o.style.cssText="\n    background: white;\n    border-radius: 12px;\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n    max-width: 90vw;\n    max-height: 90vh;\n    overflow-y: auto;\n  ",o.innerHTML=e,n.appendChild(o),document.body.appendChild(n),n.addEventListener("click",function(e){e.target===n&&n.remove()})}document.addEventListener("DOMContentLoaded",function(){console.log("Popup loaded"),chrome.tabs.query({active:!0,currentWindow:!0},function(e){const t=e[0];t.url.includes("twitter.com")||t.url.includes("x.com")||(document.querySelector(".status").innerHTML='\n    <div style="color: #f45d22;">⚠️</div>\n    <span class="status-text" style="color: #f45d22;">请在 Twitter 网站上使用此扩展</span>\n  ',document.querySelectorAll(".action-button").forEach(e=>{"openTwitter"!==e.id&&(e.style.opacity="0.5",e.style.cursor="not-allowed",e.disabled=!0)}))}),document.getElementById("openTwitter").addEventListener("click",function(){chrome.tabs.create({url:"https://twitter.com"}),window.close()}),document.getElementById("viewHelp").addEventListener("click",function(){n('\n    <div style="padding: 20px; max-width: 400px;">\n      <h3 style="margin-top: 0; color: #14171a;">使用帮助</h3>\n      \n      <div style="margin-bottom: 20px;">\n        <h4 style="color: #1da1f2; margin-bottom: 8px;">如何使用：</h4>\n        <ol style="color: #657786; line-height: 1.6;">\n          <li>在 Twitter 网站上打开推文编辑框</li>\n          <li>点击出现的"AI生成"按钮</li>\n          <li>输入关键词和选择参数</li>\n          <li>点击"生成推文"获取AI建议</li>\n          <li>选择喜欢的推文内容使用</li>\n        </ol>\n      </div>\n      \n      <div style="margin-bottom: 20px;">\n        <h4 style="color: #1da1f2; margin-bottom: 8px;">功能特点：</h4>\n        <ul style="color: #657786; line-height: 1.6;">\n          <li>多种语调风格选择</li>\n          <li>智能互动率预测</li>\n          <li>实时内容优化建议</li>\n          <li>一键插入推文内容</li>\n        </ul>\n      </div>\n      \n      <div style="text-align: center;">\n        <button onclick="this.parentElement.parentElement.parentElement.remove()" \n                style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">\n          知道了\n        </button>\n      </div>\n    </div>\n  ')}),document.getElementById("viewStats").addEventListener("click",function(){chrome.storage.local.get(["usageStats"],function(e){const t=e.usageStats||{tweetsGenerated:0,tweetsUsed:0,lastUsed:null};n(`\n      <div style="padding: 20px; max-width: 400px;">\n        <h3 style="margin-top: 0; color: #14171a;">使用统计</h3>\n        \n        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">\n          <div style="text-align: center; padding: 16px; background: #f7f9fa; border-radius: 8px;">\n            <div style="font-size: 24px; font-weight: bold; color: #1da1f2;">${t.tweetsGenerated}</div>\n            <div style="font-size: 14px; color: #657786;">生成推文数</div>\n          </div>\n          <div style="text-align: center; padding: 16px; background: #f7f9fa; border-radius: 8px;">\n            <div style="font-size: 24px; font-weight: bold; color: #17bf63;">${t.tweetsUsed}</div>\n            <div style="font-size: 14px; color: #657786;">使用推文数</div>\n          </div>\n        </div>\n        \n        <div style="margin-bottom: 20px;">\n          <div style="font-size: 14px; color: #657786;">\n            使用率: <strong style="color: #1da1f2;">${t.tweetsGenerated>0?Math.round(t.tweetsUsed/t.tweetsGenerated*100):0}%</strong>\n          </div>\n          <div style="font-size: 14px; color: #657786; margin-top: 8px;">\n            最后使用: ${t.lastUsed?new Date(t.lastUsed).toLocaleDateString("zh-CN"):"从未使用"}\n          </div>\n        </div>\n        \n        <div style="text-align: center;">\n          <button onclick="this.parentElement.parentElement.parentElement.remove()" \n                  style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">\n            关闭\n          </button>\n        </div>\n      </div>\n    `)})}),document.getElementById("enableToggle").addEventListener("click",function(){e("enabled",this)}),document.getElementById("autoDetectToggle").addEventListener("click",function(){e("autoDetect",this)}),document.getElementById("feedbackLink").addEventListener("click",function(e){e.preventDefault(),chrome.tabs.create({url:"mailto:<EMAIL>?subject=Twitter Content Generator Feedback"})}),chrome.storage.sync.get(["enabled","autoDetect"],function(e){const n=document.getElementById("enableToggle"),o=document.getElementById("autoDetectToggle");!1!==e.enabled?n.classList.add("active"):n.classList.remove("active"),!1!==e.autoDetect?o.classList.add("active"):o.classList.remove("active"),t(!1!==e.enabled)})})})();