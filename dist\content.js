(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,n(r.key),r)}}function n(t){var n=function(t){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,"string");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==e(n)?n:n+""}console.log("Twitter Content Generator: Content script loaded"),new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInjected=!1,this.observer=null,this.init()},n=[{key:"init",value:function(){var e=this;"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){return e.startObserving()}):this.startObserving()}},{key:"startObserving",value:function(){var e=this;this.observer=new MutationObserver(function(t){t.forEach(function(t){"childList"===t.type&&e.checkForComposeBox()})}),this.observer.observe(document.body,{childList:!0,subtree:!0}),this.checkForComposeBox()}},{key:"checkForComposeBox",value:function(){for(var e=null,t=0,n=['[data-testid="tweetTextarea_0"]','[data-testid="tweetButton"]',".DraftEditor-root",'[role="textbox"][data-testid="tweetTextarea_0"]',".public-DraftEditor-content"];t<n.length;t++){var o=n[t];if(e=document.querySelector(o))break}e&&!this.isInjected&&this.injectGeneratorButton(e)}},{key:"injectGeneratorButton",value:function(e){try{var t=this.findToolbar(e);if(t&&!document.getElementById("twitter-content-generator-btn")){var n=this.createGeneratorButton();t.insertBefore(n,t.firstChild),this.isInjected=!0,console.log("Twitter Content Generator: Button injected successfully")}}catch(e){console.error("Twitter Content Generator: Error injecting button",e)}}},{key:"findToolbar",value:function(e){for(var t,n=e,o=0;n&&o<10;){var r,i=n.querySelector('[data-testid="toolBar"]')||n.querySelector('[role="group"]')||n.querySelector(".css-1dbjc4n.r-18u37iz.r-1h0z5md")||(null===(r=n.parentElement)||void 0===r?void 0:r.querySelector('[data-testid="toolBar"]'));if(i)return i;n=n.parentElement,o++}return(null===(t=e.closest('[data-testid="tweetButton"]'))||void 0===t?void 0:t.parentElement)||e.parentElement}},{key:"createGeneratorButton",value:function(){var e=this,t=document.createElement("button");return t.id="twitter-content-generator-btn",t.className="twitter-generator-btn",t.innerHTML='\n      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">\n        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>\n      </svg>\n      <span>AI生成</span>\n    ',t.addEventListener("click",function(t){t.preventDefault(),t.stopPropagation(),e.showGeneratorModal()}),t}},{key:"showGeneratorModal",value:function(){var e=document.getElementById("twitter-generator-modal");e&&e.remove();var t=document.createElement("div");t.id="twitter-generator-modal",t.className="twitter-generator-modal";var n=document.createElement("div");n.id="twitter-generator-content",t.appendChild(n),t.addEventListener("click",function(e){e.target===t&&t.remove()}),document.body.appendChild(t),this.loadReactComponent(n)}},{key:"loadReactComponent",value:function(e){console.log("Loading component for container:",e.id),chrome.runtime.sendMessage({action:"loadGenerator",containerId:e.id});var t=document.createElement("script");t.src=chrome.runtime.getURL("injected.js"),t.onload=function(){console.log("Injected script loaded, checking TwitterGeneratorApp..."),setTimeout(function(){window.postMessage({type:"TWITTER_GENERATOR_INIT",containerId:e.id},"*")},200);var t=function(n){"TWITTER_GENERATOR_INIT_RESPONSE"===n.data.type&&(n.data.success?console.log("TwitterGeneratorApp found, initializing..."):(console.error("TwitterGeneratorApp not found in page context!"),e.innerHTML='\n              <div style="background: white; padding: 20px; border-radius: 12px; text-align: center;">\n                <h3>加载错误</h3>\n                <p>无法加载AI生成器组件，请刷新页面重试。</p>\n                <button onclick="this.closest(\'.twitter-generator-modal\').remove()"\n                        style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">\n                  关闭\n                </button>\n              </div>\n            '),window.removeEventListener("message",t))};window.addEventListener("message",t),setTimeout(function(){window.removeEventListener("message",t)},5e3)},t.onerror=function(){console.error("Failed to load injected script"),e.innerHTML='\n        <div style="background: white; padding: 20px; border-radius: 12px; text-align: center;">\n          <h3>脚本加载失败</h3>\n          <p>请检查扩展是否正确安装。</p>\n          <button onclick="this.closest(\'.twitter-generator-modal\').remove()"\n                  style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">\n            关闭\n          </button>\n        </div>\n      '},document.head.appendChild(t)}}],n&&t(e.prototype,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n}())})();