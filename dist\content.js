console.log("Twitter Content Generator: Content script loaded"),new class{constructor(){this.isInjected=!1,this.observer=null,this.init()}init(){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>this.startObserving()):this.startObserving()}startObserving(){this.observer=new MutationObserver(e=>{e.forEach(e=>{"childList"===e.type&&this.checkForComposeBox()})}),this.observer.observe(document.body,{childList:!0,subtree:!0}),this.checkForComposeBox()}checkForComposeBox(){const e=['[data-testid="tweetTextarea_0"]','[data-testid="tweetButton"]',".DraftEditor-root",'[role="textbox"][data-testid="tweetTextarea_0"]',".public-DraftEditor-content"];let t=null;for(const n of e)if(t=document.querySelector(n),t)break;t&&!this.isInjected&&this.injectGeneratorButton(t)}injectGeneratorButton(e){try{const t=this.findToolbar(e);if(t&&!document.getElementById("twitter-content-generator-btn")){const e=this.createGeneratorButton();t.insertBefore(e,t.firstChild),this.isInjected=!0,console.log("Twitter Content Generator: Button injected successfully")}}catch(e){console.error("Twitter Content Generator: Error injecting button",e)}}findToolbar(e){let t=e,n=0;for(;t&&n<10;){const e=t.querySelector('[data-testid="toolBar"]')||t.querySelector('[role="group"]')||t.querySelector(".css-1dbjc4n.r-18u37iz.r-1h0z5md")||t.parentElement?.querySelector('[data-testid="toolBar"]');if(e)return e;t=t.parentElement,n++}return e.closest('[data-testid="tweetButton"]')?.parentElement||e.parentElement}createGeneratorButton(){const e=document.createElement("button");return e.id="twitter-content-generator-btn",e.className="twitter-generator-btn",e.innerHTML='\n      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">\n        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>\n      </svg>\n      <span>AI生成</span>\n    ',e.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation(),this.showGeneratorModal()}),e}showGeneratorModal(){const e=document.getElementById("twitter-generator-modal");e&&e.remove();const t=document.createElement("div");t.id="twitter-generator-modal",t.className="twitter-generator-modal";const n=document.createElement("div");n.id="twitter-generator-content",t.appendChild(n),t.addEventListener("click",e=>{e.target===t&&t.remove()}),document.body.appendChild(t),this.loadGeneratorComponent(n)}loadGeneratorComponent(e){console.log("Loading component for container:",e.id),chrome.runtime.sendMessage({action:"loadGenerator",containerId:e.id});const t=document.createElement("script");t.src=chrome.runtime.getURL("injected.js"),t.onload=()=>{console.log("Injected script loaded, checking TwitterGeneratorApp..."),setTimeout(()=>{window.postMessage({type:"TWITTER_GENERATOR_INIT",containerId:e.id},"*")},200);const t=n=>{"TWITTER_GENERATOR_INIT_RESPONSE"===n.data.type&&(n.data.success?console.log("TwitterGeneratorApp found, initializing..."):(console.error("TwitterGeneratorApp not found in page context!"),e.innerHTML='\n              <div style="background: white; padding: 20px; border-radius: 12px; text-align: center;">\n                <h3>加载错误</h3>\n                <p>无法加载AI生成器组件，请刷新页面重试。</p>\n                <button onclick="this.closest(\'.twitter-generator-modal\').remove()"\n                        style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">\n                  关闭\n                </button>\n              </div>\n            '),window.removeEventListener("message",t))};window.addEventListener("message",t),setTimeout(()=>{window.removeEventListener("message",t)},5e3)},t.onerror=()=>{console.error("Failed to load injected script"),e.innerHTML='\n        <div style="background: white; padding: 20px; border-radius: 12px; text-align: center;">\n          <h3>脚本加载失败</h3>\n          <p>请检查扩展是否正确安装。</p>\n          <button onclick="this.closest(\'.twitter-generator-modal\').remove()"\n                  style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">\n            关闭\n          </button>\n        </div>\n      '},document.head.appendChild(t)}};
//# sourceMappingURL=content.js.map