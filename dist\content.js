(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,n(o.key),o)}}function n(e){var n=function(e){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(n)?n:n+""}console.log("Twitter Content Generator: Content script loaded"),new(function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.isInjected=!1,this.observer=null,this.init()},n=[{key:"init",value:function(){var t=this;"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){return t.startObserving()}):this.startObserving()}},{key:"startObserving",value:function(){var t=this;this.observer=new MutationObserver(function(e){e.forEach(function(e){"childList"===e.type&&t.checkForComposeBox()})}),this.observer.observe(document.body,{childList:!0,subtree:!0}),this.checkForComposeBox()}},{key:"checkForComposeBox",value:function(){for(var t=null,e=0,n=['[data-testid="tweetTextarea_0"]','[data-testid="tweetButton"]',".DraftEditor-root",'[role="textbox"][data-testid="tweetTextarea_0"]',".public-DraftEditor-content"];e<n.length;e++){var r=n[e];if(t=document.querySelector(r))break}t&&!this.isInjected&&this.injectGeneratorButton(t)}},{key:"injectGeneratorButton",value:function(t){try{var e=this.findToolbar(t);if(e&&!document.getElementById("twitter-content-generator-btn")){var n=this.createGeneratorButton();e.insertBefore(n,e.firstChild),this.isInjected=!0,console.log("Twitter Content Generator: Button injected successfully")}}catch(t){console.error("Twitter Content Generator: Error injecting button",t)}}},{key:"findToolbar",value:function(t){for(var e,n=t,r=0;n&&r<10;){var o,i=n.querySelector('[data-testid="toolBar"]')||n.querySelector('[role="group"]')||n.querySelector(".css-1dbjc4n.r-18u37iz.r-1h0z5md")||(null===(o=n.parentElement)||void 0===o?void 0:o.querySelector('[data-testid="toolBar"]'));if(i)return i;n=n.parentElement,r++}return(null===(e=t.closest('[data-testid="tweetButton"]'))||void 0===e?void 0:e.parentElement)||t.parentElement}},{key:"createGeneratorButton",value:function(){var t=this,e=document.createElement("button");return e.id="twitter-content-generator-btn",e.className="twitter-generator-btn",e.innerHTML='\n      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">\n        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>\n      </svg>\n      <span>AI生成</span>\n    ',e.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation(),t.showGeneratorModal()}),e}},{key:"showGeneratorModal",value:function(){var t=document.getElementById("twitter-generator-modal");t&&t.remove();var e=document.createElement("div");e.id="twitter-generator-modal",e.className="twitter-generator-modal",document.body.appendChild(e),this.loadReactComponent(e)}},{key:"loadReactComponent",value:function(t){chrome.runtime.sendMessage({action:"loadGenerator",containerId:t.id});var e=document.createElement("script");e.src=chrome.runtime.getURL("injected.js"),e.onload=function(){window.TwitterGeneratorApp&&window.TwitterGeneratorApp.init(t.id)},document.head.appendChild(e)}}],n&&e(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n}())})();