# Twitter Content Generator - 安装指南

## 🚀 快速安装

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在Chrome浏览器地址栏输入：`chrome://extensions/`
   - 或者点击右上角三点菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开关启用开发者模式

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择项目的 `dist` 文件夹
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展应该出现在扩展列表中
   - 确保扩展已启用（开关为蓝色）

### 方法二：打包安装

1. **打包扩展**
   - 在Chrome扩展管理页面，点击"打包扩展程序"
   - 选择 `dist` 文件夹作为扩展根目录
   - 点击"打包扩展程序"
   - 会生成 `.crx` 文件

2. **安装打包文件**
   - 将生成的 `.crx` 文件拖拽到Chrome扩展页面
   - 点击"添加扩展程序"确认安装

## 🔧 使用前准备

### 1. 权限确认
安装后，扩展需要以下权限：
- ✅ 访问 twitter.com 和 x.com
- ✅ 存储设置数据
- ✅ 在活动标签页中运行脚本

### 2. API配置（可选）
如果要使用自定义API：
1. 点击扩展图标打开设置面板
2. 在设置中配置API密钥
3. 或者修改 `src/services/apiService.js` 中的配置

## 📱 使用方法

### 基础使用流程

1. **访问Twitter**
   - 打开 [twitter.com](https://twitter.com) 或 [x.com](https://x.com)
   - 登录你的Twitter账户

2. **开始创建推文**
   - 点击"发推文"按钮
   - 或者回复任何推文

3. **使用AI生成功能**
   - 在推文编辑框附近会出现蓝色的"AI生成"按钮
   - 点击按钮打开生成器界面

4. **配置生成参数**
   - **关键词**：输入要讨论的主题（必填）
   - **语调风格**：选择专业、轻松、幽默、励志或争议
   - **推文长度**：选择简短、中等或较长

5. **生成和选择**
   - 点击"生成推文"按钮
   - 查看生成的多个推文选项
   - 每个选项都有互动率预测
   - 点击"使用这条推文"将内容插入编辑框

### 高级功能

- **右键菜单**：选中任何文本后右键，选择"生成推文"
- **设置面板**：点击扩展图标查看使用统计和设置
- **快捷操作**：在设置面板中可以快速打开Twitter

## 🛠️ 故障排除

### 常见问题

**Q: 扩展按钮没有出现**
- 确保你在 twitter.com 或 x.com 网站上
- 刷新页面重试
- 检查扩展是否已启用

**Q: 生成功能不工作**
- 检查网络连接
- 查看浏览器控制台是否有错误信息
- 尝试重新加载扩展

**Q: 无法插入生成的推文**
- 确保推文编辑框处于活动状态
- 尝试手动点击编辑框后再使用生成功能
- 如果仍然失败，内容会自动复制到剪贴板

**Q: 扩展图标显示为灰色**
- 这表示当前页面不是Twitter网站
- 导航到 twitter.com 或 x.com 即可正常使用

### 开发者调试

如果你是开发者，可以通过以下方式调试：

1. **查看控制台日志**
   - 按F12打开开发者工具
   - 查看Console标签页的日志信息

2. **检查扩展状态**
   - 访问 `chrome://extensions/`
   - 点击扩展的"详细信息"
   - 查看"检查视图"部分的链接

3. **重新加载扩展**
   - 在扩展管理页面点击刷新按钮
   - 或者先移除再重新加载

## 📞 支持与反馈

如果遇到问题或有改进建议：

1. **检查README.md**：查看详细的技术文档
2. **提交Issue**：在项目仓库中创建问题报告
3. **联系开发者**：通过项目页面联系方式

## 🔄 更新扩展

### 开发版本更新
1. 拉取最新代码
2. 运行 `npm run build`
3. 在扩展管理页面点击刷新按钮

### 发布版本更新
- Chrome会自动检查和安装更新
- 或者手动重新安装新版本

---

**享受使用Twitter Content Generator！** 🎉
