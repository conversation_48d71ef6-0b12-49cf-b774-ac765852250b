// Content script for Twitter Content Generator
console.log('Twitter Content Generator: Content script loaded');

class TwitterContentGenerator {
  constructor() {
    this.isInjected = false;
    this.observer = null;
    this.init();
  }

  init() {
    // Wait for page to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.startObserving());
    } else {
      this.startObserving();
    }
  }

  startObserving() {
    // Create mutation observer to watch for Twitter compose boxes
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          this.checkForComposeBox();
        }
      });
    });

    // Start observing
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Initial check
    this.checkForComposeBox();
  }

  checkForComposeBox() {
    // Twitter compose box selectors (multiple possible selectors for different Twitter versions)
    const selectors = [
      '[data-testid="tweetTextarea_0"]',
      '[data-testid="tweetButton"]',
      '.DraftEditor-root',
      '[role="textbox"][data-testid="tweetTextarea_0"]',
      '.public-DraftEditor-content'
    ];

    let composeBox = null;
    for (const selector of selectors) {
      composeBox = document.querySelector(selector);
      if (composeBox) break;
    }

    if (composeBox && !this.isInjected) {
      this.injectGeneratorButton(composeBox);
    }
  }

  injectGeneratorButton(composeBox) {
    try {
      // Find the toolbar area (usually contains tweet button, emoji button, etc.)
      const toolbar = this.findToolbar(composeBox);
      
      if (toolbar && !document.getElementById('twitter-content-generator-btn')) {
        // Create the generator button
        const generatorBtn = this.createGeneratorButton();
        
        // Insert the button into the toolbar
        toolbar.insertBefore(generatorBtn, toolbar.firstChild);
        
        this.isInjected = true;
        console.log('Twitter Content Generator: Button injected successfully');
      }
    } catch (error) {
      console.error('Twitter Content Generator: Error injecting button', error);
    }
  }

  findToolbar(composeBox) {
    // Try to find the toolbar by traversing up and looking for common patterns
    let current = composeBox;
    let attempts = 0;
    
    while (current && attempts < 10) {
      // Look for toolbar indicators
      const toolbar = current.querySelector('[data-testid="toolBar"]') ||
                     current.querySelector('[role="group"]') ||
                     current.querySelector('.css-1dbjc4n.r-18u37iz.r-1h0z5md') ||
                     current.parentElement?.querySelector('[data-testid="toolBar"]');
      
      if (toolbar) {
        return toolbar;
      }
      
      current = current.parentElement;
      attempts++;
    }
    
    // Fallback: create our own container
    const container = composeBox.closest('[data-testid="tweetButton"]')?.parentElement ||
                     composeBox.parentElement;
    return container;
  }

  createGeneratorButton() {
    const button = document.createElement('button');
    button.id = 'twitter-content-generator-btn';
    button.className = 'twitter-generator-btn';
    button.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
      <span>AI生成</span>
    `;
    
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.showGeneratorModal();
    });
    
    return button;
  }

  showGeneratorModal() {
    // Remove existing modal if present
    const existingModal = document.getElementById('twitter-generator-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal container
    const modal = document.createElement('div');
    modal.id = 'twitter-generator-modal';
    modal.className = 'twitter-generator-modal';
    
    // Inject the React component
    document.body.appendChild(modal);
    
    // Load the injected script that contains the React component
    this.loadReactComponent(modal);
  }

  loadReactComponent(container) {
    // Send message to background script to load React component
    chrome.runtime.sendMessage({
      action: 'loadGenerator',
      containerId: container.id
    });

    // Also directly inject the React component script
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('injected.js');
    script.onload = () => {
      // Initialize the React component
      if (window.TwitterGeneratorApp) {
        window.TwitterGeneratorApp.init(container.id);
      }
    };
    document.head.appendChild(script);
  }
}

// Initialize the content generator
new TwitterContentGenerator();
