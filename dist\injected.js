/*! For license information please see injected.js.LICENSE.txt */
(()=>{function e(){var t,o,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",i=r.toStringTag||"@@toStringTag";function l(e,r,a,i){var l=r&&r.prototype instanceof c?r:c,u=Object.create(l.prototype);return n(u,"_invoke",function(e,n,r){var a,i,l,c=0,u=r||[],d=!1,p={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,n){return a=e,i=0,l=t,p.n=n,s}};function f(e,n){for(i=e,l=n,o=0;!d&&c&&!r&&o<u.length;o++){var r,a=u[o],f=p.p,g=a[2];e>3?(r=g===n)&&(l=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=t):a[0]<=f&&((r=e<2&&f<a[1])?(i=0,p.v=n,p.n=a[1]):f<g&&(r=e<3||a[0]>n||n>g)&&(a[4]=e,a[5]=n,p.n=g,i=0))}if(r||e>1)return s;throw d=!0,n}return function(r,u,g){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,g),i=u,l=g;(o=i<2?t:l)||!d;){a||(i?i<3?(i>1&&(p.n=-1),f(i,l)):p.n=l:p.v=l);try{if(c=2,a){if(i||(r="next"),o=a[r]){if(!(o=o.call(a,l)))throw TypeError("iterator result is not an object");if(!o.done)return o;l=o.value,i<2&&(i=0)}else 1===i&&(o=a.return)&&o.call(a),i<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),i=1);a=t}else if((o=(d=p.n<0)?l:e.call(n,p))!==s)break}catch(e){a=t,i=1,l=e}finally{c=1}}return{value:o,done:d}}}(e,a,i),!0),u}var s={};function c(){}function u(){}function d(){}o=Object.getPrototypeOf;var p=[][a]?o(o([][a]())):(n(o={},a,function(){return this}),o),f=d.prototype=c.prototype=Object.create(p);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,n(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=d,n(f,"constructor",d),n(d,"constructor",u),u.displayName="GeneratorFunction",n(d,i,"GeneratorFunction"),n(f),n(f,i,"Generator"),n(f,a,function(){return this}),n(f,"toString",function(){return"[object Generator]"}),(e=function(){return{w:l,m:g}})()}function n(e,t,o,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}n=function(e,t,o,r){if(t)a?a(e,t,{value:o,enumerable:!r,configurable:!r,writable:!r}):e[t]=o;else{var i=function(t,o){n(e,t,function(e){return this._invoke(t,o,e)})};i("next",0),i("throw",1),i("return",2)}},n(e,t,o,r)}function t(e,n,t,o,r,a,i){try{var l=e[a](i),s=l.value}catch(e){return void t(e)}l.done?n(s):Promise.resolve(s).then(o,r)}function o(e){return function(){var n=this,o=arguments;return new Promise(function(r,a){var i=e.apply(n,o);function l(e){t(i,r,a,l,s,"next",e)}function s(e){t(i,r,a,l,s,"throw",e)}l(void 0)})}}window.TwitterGeneratorApp={currentModal:null,init:function(e){var n=document.getElementById(e);n?(this.createGeneratorUI(n),this.currentModal=n):console.error("Container not found:",e)},createGeneratorUI:function(e){e.innerHTML='\n      <div class="generator-modal-content">\n        <div class="generator-modal-header">\n          <h2 class="generator-modal-title">AI推文生成器</h2>\n          <button class="generator-close-btn" onclick="window.TwitterGeneratorApp.close()">\n            ×\n          </button>\n        </div>\n\n        <form class="generator-form" onsubmit="window.TwitterGeneratorApp.handleGenerate(event)">\n          <div class="generator-input-group">\n            <label class="generator-label" for="keywords">\n              关键词 *\n            </label>\n            <input\n              id="keywords"\n              type="text"\n              class="generator-input"\n              placeholder="输入要讨论的主题或关键词..."\n              required\n            />\n          </div>\n\n          <div class="generator-input-group">\n            <label class="generator-label" for="tone">\n              语调风格\n            </label>\n            <select id="tone" class="generator-input">\n              <option value="professional">专业</option>\n              <option value="casual">轻松</option>\n              <option value="humorous">幽默</option>\n              <option value="inspirational">励志</option>\n              <option value="controversial">争议</option>\n            </select>\n          </div>\n\n          <div class="generator-input-group">\n            <label class="generator-label" for="length">\n              推文长度\n            </label>\n            <select id="length" class="generator-input">\n              <option value="short">简短 (50-100字)</option>\n              <option value="medium" selected>中等 (100-200字)</option>\n              <option value="long">较长 (200-280字)</option>\n            </select>\n          </div>\n\n          <button type="submit" class="generator-generate-btn" id="generateBtn">\n            生成推文\n          </button>\n        </form>\n\n        <div id="loadingDiv" class="generator-loading" style="display: none;">\n          <div class="generator-spinner"></div>\n          <span>正在生成推文...</span>\n        </div>\n\n        <div id="resultsDiv" class="generator-results" style="display: none;">\n          <h3 style="margin-bottom: 16px; fontSize: 18px; font-weight: 600;">\n            生成结果\n          </h3>\n          <div id="resultsContainer"></div>\n        </div>\n      </div>\n    '},close:function(){this.currentModal&&(this.currentModal.remove(),this.currentModal=null)},handleGenerate:function(n){var t=this;return o(e().m(function o(){var r,a,i,l,s;return e().w(function(e){for(;;)switch(e.n){case 0:if(n.preventDefault(),r=document.getElementById("keywords").value,a=document.getElementById("tone").value,i=document.getElementById("length").value,r.trim()){e.n=1;break}return alert("请输入关键词"),e.a(2);case 1:return document.getElementById("loadingDiv").style.display="flex",document.getElementById("resultsDiv").style.display="none",document.getElementById("generateBtn").disabled=!0,e.p=2,e.n=3,t.generateTweets({keywords:r,tone:a,length:i});case 3:l=e.v,t.displayResults(l),e.n=5;break;case 4:e.p=4,s=e.v,console.error("Error generating tweets:",s),alert("生成推文时出错，请重试");case 5:return e.p=5,document.getElementById("loadingDiv").style.display="none",document.getElementById("generateBtn").disabled=!1,e.f(5);case 6:return e.a(2)}},o,null,[[2,4,5,6]])}))()},generateTweets:function(n){return o(e().m(function t(){return e().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e){setTimeout(function(){var t=[{text:"关于".concat(n.keywords,"的").concat({professional:"专业",casual:"轻松",humorous:"幽默",inspirational:"励志",controversial:"争议"}[n.tone],"思考：这个话题值得我们深入探讨。每个人都有自己独特的见解，让我们一起分享和学习。#").concat(n.keywords," #思考"),engagement:{likes:Math.floor(100*Math.random())+20,retweets:Math.floor(50*Math.random())+10,replies:Math.floor(30*Math.random())+5,engagementRate:(5*Math.random()+2).toFixed(1)}},{text:"".concat(n.keywords,"让我想到了很多。在这个快速变化的时代，我们需要保持开放的心态，不断学习和成长。你们怎么看？"),engagement:{likes:Math.floor(80*Math.random())+15,retweets:Math.floor(40*Math.random())+8,replies:Math.floor(25*Math.random())+3,engagementRate:(4*Math.random()****).toFixed(1)}},{text:"今天想和大家聊聊".concat(n.keywords,"。这个话题很有意思，希望能听到更多不同的声音和观点。互动是最好的学习方式！"),engagement:{likes:Math.floor(120*Math.random())+25,retweets:Math.floor(60*Math.random())+12,replies:Math.floor(35*Math.random())+8,engagementRate:(6*Math.random()****).toFixed(1)}}];e(t)},1500)}))},t)}))()},displayResults:function(e){var n=document.getElementById("resultsContainer");n.innerHTML="",e.forEach(function(e,t){var o=document.createElement("div");o.className="generator-tweet-result",o.innerHTML='\n        <div class="generator-tweet-text">\n          '.concat(e.text,'\n        </div>\n        <div class="generator-tweet-stats">\n          <span>预测互动：</span>\n          <span>👍 ').concat(e.engagement.likes,"</span>\n          <span>🔄 ").concat(e.engagement.retweets,"</span>\n          <span>💬 ").concat(e.engagement.replies,"</span>\n          <span>📊 ").concat(e.engagement.engagementRate,'%</span>\n        </div>\n        <button class="generator-use-btn" onclick="window.TwitterGeneratorApp.useTweet(\'').concat(e.text.replace(/'/g,"\\'"),"')\">\n          使用这条推文\n        </button>\n      "),n.appendChild(o)}),document.getElementById("resultsDiv").style.display="block"},useTweet:function(e){for(var n=null,t=0,o=['[data-testid="tweetTextarea_0"]',".DraftEditor-root .public-DraftEditor-content",'[role="textbox"][data-testid="tweetTextarea_0"]'];t<o.length;t++){var r=o[t];if(n=document.querySelector(r))break}if(n){n.focus(),document.execCommand("selectAll"),document.execCommand("delete"),document.execCommand("insertText",!1,e);var a=new Event("input",{bubbles:!0});n.dispatchEvent(a),console.log("Tweet text inserted successfully")}else console.error("Could not find Twitter compose box"),navigator.clipboard.writeText(e).then(function(){alert("推文已复制到剪贴板，请手动粘贴到推文框中")})}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){console.log("Twitter Generator injected script loaded")}):console.log("Twitter Generator injected script loaded")})();