console.log("Creating TwitterGeneratorApp on window object..."),window.TwitterGeneratorApp={currentModal:null,init(e){const t=document.getElementById(e);t?(this.createGeneratorUI(t),this.currentModal=t):console.error("Container not found:",e)},createGeneratorUI(e){e.innerHTML='\n      <div class="generator-modal-content">\n        <div class="generator-modal-header">\n          <h2 class="generator-modal-title">AI推文生成器</h2>\n          <button class="generator-close-btn" onclick="window.TwitterGeneratorApp.close()">\n            ×\n          </button>\n        </div>\n\n        <form class="generator-form" onsubmit="window.TwitterGeneratorApp.handleGenerate(event)">\n          <div class="generator-input-group">\n            <label class="generator-label" for="keywords">\n              关键词 *\n            </label>\n            <input\n              id="keywords"\n              type="text"\n              class="generator-input"\n              placeholder="输入要讨论的主题或关键词..."\n              required\n            />\n          </div>\n\n          <div class="generator-input-group">\n            <label class="generator-label" for="tone">\n              语调风格\n            </label>\n            <select id="tone" class="generator-input">\n              <option value="professional">专业</option>\n              <option value="casual">轻松</option>\n              <option value="humorous">幽默</option>\n              <option value="inspirational">励志</option>\n              <option value="controversial">争议</option>\n            </select>\n          </div>\n\n          <div class="generator-input-group">\n            <label class="generator-label" for="length">\n              推文长度\n            </label>\n            <select id="length" class="generator-input">\n              <option value="short">简短 (50-100字)</option>\n              <option value="medium" selected>中等 (100-200字)</option>\n              <option value="long">较长 (200-280字)</option>\n            </select>\n          </div>\n\n          <button type="submit" class="generator-generate-btn" id="generateBtn">\n            生成推文\n          </button>\n        </form>\n\n        <div id="loadingDiv" class="generator-loading" style="display: none;">\n          <div class="generator-spinner"></div>\n          <span>正在生成推文...</span>\n        </div>\n\n        <div id="resultsDiv" class="generator-results" style="display: none;">\n          <h3 style="margin-bottom: 16px; fontSize: 18px; font-weight: 600;">\n            生成结果\n          </h3>\n          <div id="resultsContainer"></div>\n        </div>\n      </div>\n    '},close(){this.currentModal&&(this.currentModal.remove(),this.currentModal=null);const e=document.getElementById("twitter-generator-modal");e&&e.remove()},async handleGenerate(e){e.preventDefault();const t=document.getElementById("keywords").value,n=document.getElementById("tone").value,o=document.getElementById("length").value;if(t.trim()){document.getElementById("loadingDiv").style.display="flex",document.getElementById("resultsDiv").style.display="none",document.getElementById("generateBtn").disabled=!0;try{const e=await this.generateTweets({keywords:t,tone:n,length:o});this.displayResults(e)}catch(e){console.error("Error generating tweets:",e),alert("生成推文时出错，请重试")}finally{document.getElementById("loadingDiv").style.display="none",document.getElementById("generateBtn").disabled=!1}}else alert("请输入关键词")},generateTweets:async e=>new Promise(t=>{setTimeout(()=>{const n=[{text:`关于${e.keywords}的${{professional:"专业",casual:"轻松",humorous:"幽默",inspirational:"励志",controversial:"争议"}[e.tone]}思考：这个话题值得我们深入探讨。每个人都有自己独特的见解，让我们一起分享和学习。#${e.keywords} #思考`,engagement:{likes:Math.floor(100*Math.random())+20,retweets:Math.floor(50*Math.random())+10,replies:Math.floor(30*Math.random())+5,engagementRate:(5*Math.random()+2).toFixed(1)}},{text:`${e.keywords}让我想到了很多。在这个快速变化的时代，我们需要保持开放的心态，不断学习和成长。你们怎么看？`,engagement:{likes:Math.floor(80*Math.random())+15,retweets:Math.floor(40*Math.random())+8,replies:Math.floor(25*Math.random())+3,engagementRate:(4*Math.random()****).toFixed(1)}},{text:`今天想和大家聊聊${e.keywords}。这个话题很有意思，希望能听到更多不同的声音和观点。互动是最好的学习方式！`,engagement:{likes:Math.floor(120*Math.random())+25,retweets:Math.floor(60*Math.random())+12,replies:Math.floor(35*Math.random())+8,engagementRate:(6*Math.random()****).toFixed(1)}}];t(n)},1500)}),displayResults(e){const t=document.getElementById("resultsContainer");t.innerHTML="",e.forEach((e,n)=>{const o=document.createElement("div");o.className="generator-tweet-result",o.innerHTML=`\n        <div class="generator-tweet-text">\n          ${e.text}\n        </div>\n        <div class="generator-tweet-stats">\n          <span>预测互动：</span>\n          <span>👍 ${e.engagement.likes}</span>\n          <span>🔄 ${e.engagement.retweets}</span>\n          <span>💬 ${e.engagement.replies}</span>\n          <span>📊 ${e.engagement.engagementRate}%</span>\n        </div>\n        <button class="generator-use-btn" onclick="window.TwitterGeneratorApp.useTweet('${e.text.replace(/'/g,"\\'")}')">\n          使用这条推文\n        </button>\n      `,t.appendChild(o)}),document.getElementById("resultsDiv").style.display="block"},useTweet(e){const t=['[data-testid="tweetTextarea_0"]',".DraftEditor-root .public-DraftEditor-content",'[role="textbox"][data-testid="tweetTextarea_0"]'];let n=null;for(const e of t)if(n=document.querySelector(e),n)break;if(n){n.focus(),document.execCommand("selectAll"),document.execCommand("delete"),document.execCommand("insertText",!1,e);const t=new Event("input",{bubbles:!0});n.dispatchEvent(t),console.log("Tweet text inserted successfully")}else console.error("Could not find Twitter compose box"),navigator.clipboard.writeText(e).then(()=>{alert("推文已复制到剪贴板，请手动粘贴到推文框中")})}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>{console.log("Twitter Generator injected script loaded")}):console.log("Twitter Generator injected script loaded"),console.log("TwitterGeneratorApp created successfully:",!!window.TwitterGeneratorApp);