// Background script for Twitter Content Generator Chrome Extension

console.log('Twitter Content Generator: Background script loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Twitter Content Generator installed:', details.reason);

  if (details.reason === 'install') {
    // Set default settings
    chrome.storage.sync.set({
      enabled: true,
      apiKey: '',
      defaultTone: 'professional',
      defaultLength: 'medium'
    });
  }

  // Create context menu
  try {
    chrome.contextMenus.create({
      id: 'generateTweet',
      title: '生成推文',
      contexts: ['selection'],
      documentUrlPatterns: ['https://twitter.com/*', 'https://x.com/*']
    });
  } catch (error) {
    console.error('Error creating context menu:', error);
  }
});

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'loadGenerator':
      handleLoadGenerator(request, sender, sendResponse);
      break;
    
    case 'saveSettings':
      handleSaveSettings(request, sendResponse);
      break;
    
    case 'getSettings':
      handleGetSettings(sendResponse);
      break;
    
    case 'generateTweet':
      handleGenerateTweet(request, sendResponse);
      return true; // Keep message channel open for async response
    
    case 'predictEngagement':
      handlePredictEngagement(request, sendResponse);
      return true; // Keep message channel open for async response
    
    default:
      console.warn('Unknown action:', request.action);
  }
});

function handleLoadGenerator(request, _sender, sendResponse) {
  console.log('Loading generator for container:', request.containerId);
  sendResponse({ success: true });
}

function handleSaveSettings(request, sendResponse) {
  chrome.storage.sync.set(request.settings, () => {
    if (chrome.runtime.lastError) {
      sendResponse({ success: false, error: chrome.runtime.lastError.message });
    } else {
      sendResponse({ success: true });
    }
  });
}

function handleGetSettings(sendResponse) {
  chrome.storage.sync.get(['enabled', 'apiKey', 'defaultTone', 'defaultLength'], (result) => {
    if (chrome.runtime.lastError) {
      sendResponse({ success: false, error: chrome.runtime.lastError.message });
    } else {
      sendResponse({ 
        success: true, 
        settings: {
          enabled: result.enabled !== false, // Default to true
          apiKey: result.apiKey || '',
          defaultTone: result.defaultTone || 'professional',
          defaultLength: result.defaultLength || 'medium'
        }
      });
    }
  });
}

async function handleGenerateTweet(request, sendResponse) {
  try {
    // Get API settings
    const settings = await new Promise((resolve) => {
      chrome.storage.sync.get(['apiKey'], (result) => {
        resolve(result);
      });
    });

    // For now, we'll use the simulation from the API service
    // In production, you would make the actual API call here
    const response = await simulateApiCall('generate', {
      keywords: request.keywords,
      tone: request.tone,
      length: request.length,
      apiKey: settings.apiKey
    });

    sendResponse({ success: true, data: response });
  } catch (error) {
    console.error('Error generating tweet:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handlePredictEngagement(request, sendResponse) {
  try {
    // Get API settings
    const settings = await new Promise((resolve) => {
      chrome.storage.sync.get(['apiKey'], (result) => {
        resolve(result);
      });
    });

    // For now, we'll use the simulation from the API service
    // In production, you would make the actual API call here
    const response = await simulateApiCall('predict', {
      text: request.text,
      apiKey: settings.apiKey
    });

    sendResponse({ success: true, data: response });
  } catch (error) {
    console.error('Error predicting engagement:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Simulate API calls for development
async function simulateApiCall(type, params) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (type === 'generate') {
        resolve(`Generated tweet about ${params.keywords} with ${params.tone} tone`);
      } else if (type === 'predict') {
        resolve({
          likes: Math.floor(Math.random() * 100) + 10,
          retweets: Math.floor(Math.random() * 50) + 5,
          replies: Math.floor(Math.random() * 30) + 2,
          engagementRate: (Math.random() * 10 + 1).toFixed(1)
        });
      } else {
        reject(new Error('Unknown API call type'));
      }
    }, 1000 + Math.random() * 2000);
  });
}

// Handle browser action click (when user clicks the extension icon)
chrome.action.onClicked.addListener(async (tab) => {
  try {
    // Check if we're on Twitter
    if (tab.url && (tab.url.includes('twitter.com') || tab.url.includes('x.com'))) {
      // Inject content script if not already injected
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: ['content.js']
      });
    } else {
      // Show notification that extension only works on Twitter
      if (chrome.notifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Twitter Content Generator',
          message: '此扩展只能在 Twitter 网站上使用'
        });
      }
    }
  } catch (error) {
    console.error('Error handling action click:', error);
  }
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'generateTweet' && info.selectionText && tab.id) {
    // Send selected text to content script for tweet generation
    chrome.tabs.sendMessage(tab.id, {
      action: 'generateFromSelection',
      text: info.selectionText
    }).catch(error => {
      console.error('Error sending message to content script:', error);
    });
  }
});
