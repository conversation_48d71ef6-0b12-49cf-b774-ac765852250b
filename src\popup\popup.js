// Popup script for Twitter Content Generator

document.addEventListener('DOMContentLoaded', function() {
  console.log('Popup loaded');
  
  // Initialize popup
  initializePopup();
  
  // Bind event listeners
  bindEventListeners();
  
  // Load settings
  loadSettings();
});

function initializePopup() {
  // Check if we're on Twitter
  chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
    const currentTab = tabs[0];
    const isTwitter = currentTab.url.includes('twitter.com') || currentTab.url.includes('x.com');
    
    if (!isTwitter) {
      showNotTwitterWarning();
    }
  });
}

function bindEventListeners() {
  // Open Twitter button
  document.getElementById('openTwitter').addEventListener('click', function() {
    chrome.tabs.create({ url: 'https://twitter.com' });
    window.close();
  });
  
  // View help button
  document.getElementById('viewHelp').addEventListener('click', function() {
    showHelp();
  });
  
  // View stats button
  document.getElementById('viewStats').addEventListener('click', function() {
    showStats();
  });
  
  // Enable toggle
  document.getElementById('enableToggle').addEventListener('click', function() {
    toggleSetting('enabled', this);
  });
  
  // Auto detect toggle
  document.getElementById('autoDetectToggle').addEventListener('click', function() {
    toggleSetting('autoDetect', this);
  });
  
  // Feedback link
  document.getElementById('feedbackLink').addEventListener('click', function(e) {
    e.preventDefault();
    chrome.tabs.create({ url: 'mailto:<EMAIL>?subject=Twitter Content Generator Feedback' });
  });
}

function loadSettings() {
  chrome.storage.sync.get(['enabled', 'autoDetect'], function(result) {
    // Update toggle states
    const enableToggle = document.getElementById('enableToggle');
    const autoDetectToggle = document.getElementById('autoDetectToggle');
    
    if (result.enabled !== false) { // Default to true
      enableToggle.classList.add('active');
    } else {
      enableToggle.classList.remove('active');
    }
    
    if (result.autoDetect !== false) { // Default to true
      autoDetectToggle.classList.add('active');
    } else {
      autoDetectToggle.classList.remove('active');
    }
    
    // Update status indicator
    updateStatusIndicator(result.enabled !== false);
  });
}

function toggleSetting(setting, element) {
  const isActive = element.classList.contains('active');
  const newValue = !isActive;
  
  // Update UI
  if (newValue) {
    element.classList.add('active');
  } else {
    element.classList.remove('active');
  }
  
  // Save setting
  const settings = {};
  settings[setting] = newValue;
  
  chrome.storage.sync.set(settings, function() {
    if (chrome.runtime.lastError) {
      console.error('Error saving setting:', chrome.runtime.lastError);
      // Revert UI change
      if (newValue) {
        element.classList.remove('active');
      } else {
        element.classList.add('active');
      }
    } else {
      console.log('Setting saved:', setting, newValue);
      
      // Update status if it's the main enable toggle
      if (setting === 'enabled') {
        updateStatusIndicator(newValue);
      }
    }
  });
}

function updateStatusIndicator(enabled) {
  const statusIndicator = document.querySelector('.status-indicator');
  const statusText = document.querySelector('.status-text');
  
  if (enabled) {
    statusIndicator.style.background = '#17bf63';
    statusText.textContent = '扩展已激活';
  } else {
    statusIndicator.style.background = '#f45d22';
    statusText.textContent = '扩展已禁用';
  }
}

function showNotTwitterWarning() {
  const status = document.querySelector('.status');
  status.innerHTML = `
    <div style="color: #f45d22;">⚠️</div>
    <span class="status-text" style="color: #f45d22;">请在 Twitter 网站上使用此扩展</span>
  `;
  
  // Disable action buttons
  const actionButtons = document.querySelectorAll('.action-button');
  actionButtons.forEach(button => {
    if (button.id !== 'openTwitter') {
      button.style.opacity = '0.5';
      button.style.cursor = 'not-allowed';
      button.disabled = true;
    }
  });
}

function showHelp() {
  // Create help modal
  const helpContent = `
    <div style="padding: 20px; max-width: 400px;">
      <h3 style="margin-top: 0; color: #14171a;">使用帮助</h3>
      
      <div style="margin-bottom: 20px;">
        <h4 style="color: #1da1f2; margin-bottom: 8px;">如何使用：</h4>
        <ol style="color: #657786; line-height: 1.6;">
          <li>在 Twitter 网站上打开推文编辑框</li>
          <li>点击出现的"AI生成"按钮</li>
          <li>输入关键词和选择参数</li>
          <li>点击"生成推文"获取AI建议</li>
          <li>选择喜欢的推文内容使用</li>
        </ol>
      </div>
      
      <div style="margin-bottom: 20px;">
        <h4 style="color: #1da1f2; margin-bottom: 8px;">功能特点：</h4>
        <ul style="color: #657786; line-height: 1.6;">
          <li>多种语调风格选择</li>
          <li>智能互动率预测</li>
          <li>实时内容优化建议</li>
          <li>一键插入推文内容</li>
        </ul>
      </div>
      
      <div style="text-align: center;">
        <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">
          知道了
        </button>
      </div>
    </div>
  `;
  
  showModal(helpContent);
}

function showStats() {
  // Get usage stats from storage
  chrome.storage.local.get(['usageStats'], function(result) {
    const stats = result.usageStats || {
      tweetsGenerated: 0,
      tweetsUsed: 0,
      lastUsed: null
    };
    
    const statsContent = `
      <div style="padding: 20px; max-width: 400px;">
        <h3 style="margin-top: 0; color: #14171a;">使用统计</h3>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
          <div style="text-align: center; padding: 16px; background: #f7f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #1da1f2;">${stats.tweetsGenerated}</div>
            <div style="font-size: 14px; color: #657786;">生成推文数</div>
          </div>
          <div style="text-align: center; padding: 16px; background: #f7f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #17bf63;">${stats.tweetsUsed}</div>
            <div style="font-size: 14px; color: #657786;">使用推文数</div>
          </div>
        </div>
        
        <div style="margin-bottom: 20px;">
          <div style="font-size: 14px; color: #657786;">
            使用率: <strong style="color: #1da1f2;">${stats.tweetsGenerated > 0 ? Math.round((stats.tweetsUsed / stats.tweetsGenerated) * 100) : 0}%</strong>
          </div>
          <div style="font-size: 14px; color: #657786; margin-top: 8px;">
            最后使用: ${stats.lastUsed ? new Date(stats.lastUsed).toLocaleDateString('zh-CN') : '从未使用'}
          </div>
        </div>
        
        <div style="text-align: center;">
          <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                  style="background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;">
            关闭
          </button>
        </div>
      </div>
    `;
    
    showModal(statsContent);
  });
}

function showModal(content) {
  // Remove existing modal
  const existingModal = document.getElementById('popup-modal');
  if (existingModal) {
    existingModal.remove();
  }
  
  // Create modal
  const modal = document.createElement('div');
  modal.id = 'popup-modal';
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
  `;
  
  const modalContent = document.createElement('div');
  modalContent.style.cssText = `
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
  `;
  
  modalContent.innerHTML = content;
  modal.appendChild(modalContent);
  document.body.appendChild(modal);
  
  // Close on background click
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      modal.remove();
    }
  });
}
