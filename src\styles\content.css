/* Twitter Content Generator Styles */

.twitter-generator-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 8px;
  box-shadow: 0 2px 4px rgba(29, 161, 242, 0.2);
}

.twitter-generator-btn:hover {
  background: linear-gradient(135deg, #0d8bd9, #0a7bc4);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(29, 161, 242, 0.3);
}

.twitter-generator-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(29, 161, 242, 0.2);
}

.twitter-generator-btn svg {
  width: 16px;
  height: 16px;
}

/* Modal Styles */
.twitter-generator-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.generator-modal-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  position: relative;
}

.generator-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e1e8ed;
}

.generator-modal-title {
  font-size: 20px;
  font-weight: 700;
  color: #14171a;
  margin: 0;
}

.generator-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #657786;
  padding: 4px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.generator-close-btn:hover {
  background-color: #f7f9fa;
}

/* Form Styles */
.generator-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.generator-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.generator-label {
  font-size: 14px;
  font-weight: 600;
  color: #14171a;
}

.generator-input {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  outline: none;
}

.generator-input:focus {
  border-color: #1da1f2;
  box-shadow: 0 0 0 3px rgba(29, 161, 242, 0.1);
}

.generator-textarea {
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.generator-generate-btn {
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.generator-generate-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0d8bd9, #0a7bc4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(29, 161, 242, 0.3);
}

.generator-generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Results Styles */
.generator-results {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.generator-result-item {
  background: #f7f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e1e8ed;
}

.generator-tweet-result {
  background: #f7f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e1e8ed;
}

.generator-tweet-text {
  font-size: 16px;
  line-height: 1.4;
  color: #14171a;
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.generator-tweet-stats {
  display: flex;
  gap: 12px;
  font-size: 14px;
  color: #657786;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.generator-tweet-stats span:first-child {
  font-weight: 600;
  color: #14171a;
}

.generator-engagement-prediction {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #657786;
}

.generator-metric {
  display: flex;
  align-items: center;
  gap: 4px;
}

.generator-metric-value {
  font-weight: 600;
  color: #1da1f2;
}

.generator-use-btn {
  background: #1da1f2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.2s ease;
}

.generator-use-btn:hover {
  background: #0d8bd9;
}

/* Loading Styles */
.generator-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #657786;
}

.generator-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e1e8ed;
  border-top: 2px solid #1da1f2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
