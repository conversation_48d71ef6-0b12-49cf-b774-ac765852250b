<!DOCTYPE html>
<html>
<head>
    <title>Create Icons</title>
</head>
<body>
    <canvas id="canvas16" width="16" height="16"></canvas>
    <canvas id="canvas48" width="48" height="48"></canvas>
    <canvas id="canvas128" width="128" height="128"></canvas>
    
    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#1da1f2');
            gradient.addColorStop(1, '#0d8bd9');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.2);
            ctx.fill();
            
            // Reset composite operation
            ctx.globalCompositeOperation = 'source-over';
            
            // Add robot emoji or AI symbol
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🤖', size/2, size/2);
            
            // Download the image
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Create all icons
        setTimeout(() => {
            createIcon('canvas16', 16);
            setTimeout(() => createIcon('canvas48', 48), 100);
            setTimeout(() => createIcon('canvas128', 128), 200);
        }, 100);
    </script>
</body>
</html>
