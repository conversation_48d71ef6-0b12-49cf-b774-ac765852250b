{"version": 3, "file": "content.js", "mappings": "AACA", "sources": ["webpack://twitter-content-generator/./src/content/content.js"], "sourcesContent": ["// Content script for Twitter Content Generator\nconsole.log('Twitter Content Generator: Content script loaded');\n\nclass TwitterContentGenerator {\n  constructor() {\n    this.isInjected = false;\n    this.observer = null;\n    this.init();\n  }\n\n  init() {\n    // Wait for page to load\n    if (document.readyState === 'loading') {\n      document.addEventListener('DOMContentLoaded', () => this.startObserving());\n    } else {\n      this.startObserving();\n    }\n  }\n\n  startObserving() {\n    // Create mutation observer to watch for Twitter compose boxes\n    this.observer = new MutationObserver((mutations) => {\n      mutations.forEach((mutation) => {\n        if (mutation.type === 'childList') {\n          this.checkForComposeBox();\n        }\n      });\n    });\n\n    // Start observing\n    this.observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n\n    // Initial check\n    this.checkForComposeBox();\n  }\n\n  checkForComposeBox() {\n    // Twitter compose box selectors (multiple possible selectors for different Twitter versions)\n    const selectors = [\n      '[data-testid=\"tweetTextarea_0\"]',\n      '[data-testid=\"tweetButton\"]',\n      '.DraftEditor-root',\n      '[role=\"textbox\"][data-testid=\"tweetTextarea_0\"]',\n      '.public-DraftEditor-content'\n    ];\n\n    let composeBox = null;\n    for (const selector of selectors) {\n      composeBox = document.querySelector(selector);\n      if (composeBox) break;\n    }\n\n    if (composeBox && !this.isInjected) {\n      this.injectGeneratorButton(composeBox);\n    }\n  }\n\n  injectGeneratorButton(composeBox) {\n    try {\n      // Find the toolbar area (usually contains tweet button, emoji button, etc.)\n      const toolbar = this.findToolbar(composeBox);\n      \n      if (toolbar && !document.getElementById('twitter-content-generator-btn')) {\n        // Create the generator button\n        const generatorBtn = this.createGeneratorButton();\n        \n        // Insert the button into the toolbar\n        toolbar.insertBefore(generatorBtn, toolbar.firstChild);\n        \n        this.isInjected = true;\n        console.log('Twitter Content Generator: Button injected successfully');\n      }\n    } catch (error) {\n      console.error('Twitter Content Generator: Error injecting button', error);\n    }\n  }\n\n  findToolbar(composeBox) {\n    // Try to find the toolbar by traversing up and looking for common patterns\n    let current = composeBox;\n    let attempts = 0;\n    \n    while (current && attempts < 10) {\n      // Look for toolbar indicators\n      const toolbar = current.querySelector('[data-testid=\"toolBar\"]') ||\n                     current.querySelector('[role=\"group\"]') ||\n                     current.querySelector('.css-1dbjc4n.r-18u37iz.r-1h0z5md') ||\n                     current.parentElement?.querySelector('[data-testid=\"toolBar\"]');\n      \n      if (toolbar) {\n        return toolbar;\n      }\n      \n      current = current.parentElement;\n      attempts++;\n    }\n    \n    // Fallback: create our own container\n    const container = composeBox.closest('[data-testid=\"tweetButton\"]')?.parentElement ||\n                     composeBox.parentElement;\n    return container;\n  }\n\n  createGeneratorButton() {\n    const button = document.createElement('button');\n    button.id = 'twitter-content-generator-btn';\n    button.className = 'twitter-generator-btn';\n    button.innerHTML = `\n      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n        <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n      </svg>\n      <span>AI生成</span>\n    `;\n    \n    button.addEventListener('click', (e) => {\n      e.preventDefault();\n      e.stopPropagation();\n      this.showGeneratorModal();\n    });\n    \n    return button;\n  }\n\n  showGeneratorModal() {\n    // Remove existing modal if present\n    const existingModal = document.getElementById('twitter-generator-modal');\n    if (existingModal) {\n      existingModal.remove();\n    }\n\n    // Create modal container\n    const modal = document.createElement('div');\n    modal.id = 'twitter-generator-modal';\n    modal.className = 'twitter-generator-modal';\n\n    // Create content container for the injected script\n    const contentContainer = document.createElement('div');\n    contentContainer.id = 'twitter-generator-content';\n    modal.appendChild(contentContainer);\n\n    // Add click handler to close modal when clicking overlay\n    modal.addEventListener('click', (e) => {\n      if (e.target === modal) {\n        modal.remove();\n      }\n    });\n\n    // Inject the modal into page\n    document.body.appendChild(modal);\n\n    // Load the injected script that contains the UI\n    this.loadGeneratorComponent(contentContainer);\n  }\n\n  loadGeneratorComponent(container) {\n    console.log('Loading component for container:', container.id);\n\n    // Send message to background script to load generator component\n    chrome.runtime.sendMessage({\n      action: 'loadGenerator',\n      containerId: container.id\n    });\n\n    // Also directly inject the component script\n    const script = document.createElement('script');\n    script.src = chrome.runtime.getURL('injected.js');\n    script.onload = () => {\n      console.log('Injected script loaded, checking TwitterGeneratorApp...');\n\n      // Use window.postMessage to communicate with the injected script\n      // Send initialization message\n      setTimeout(() => {\n        window.postMessage({\n          type: 'TWITTER_GENERATOR_INIT',\n          containerId: container.id\n        }, '*');\n      }, 200);\n\n      // Listen for response from injected script\n      const messageListener = (event) => {\n        if (event.data.type === 'TWITTER_GENERATOR_INIT_RESPONSE') {\n          if (event.data.success) {\n            console.log('TwitterGeneratorApp found, initializing...');\n          } else {\n            console.error('TwitterGeneratorApp not found in page context!');\n            // Create error message\n            container.innerHTML = `\n              <div style=\"background: white; padding: 20px; border-radius: 12px; text-align: center;\">\n                <h3>加载错误</h3>\n                <p>无法加载AI生成器组件，请刷新页面重试。</p>\n                <button onclick=\"this.closest('.twitter-generator-modal').remove()\"\n                        style=\"background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;\">\n                  关闭\n                </button>\n              </div>\n            `;\n          }\n          // Remove listener after handling\n          window.removeEventListener('message', messageListener);\n        }\n      };\n\n      window.addEventListener('message', messageListener);\n\n      // Timeout fallback\n      setTimeout(() => {\n        window.removeEventListener('message', messageListener);\n      }, 5000);\n    };\n\n    script.onerror = () => {\n      console.error('Failed to load injected script');\n      container.innerHTML = `\n        <div style=\"background: white; padding: 20px; border-radius: 12px; text-align: center;\">\n          <h3>脚本加载失败</h3>\n          <p>请检查扩展是否正确安装。</p>\n          <button onclick=\"this.closest('.twitter-generator-modal').remove()\"\n                  style=\"background: #1da1f2; color: white; border: none; padding: 8px 16px; border-radius: 16px; cursor: pointer;\">\n            关闭\n          </button>\n        </div>\n      `;\n    };\n\n    document.head.appendChild(script);\n  }\n}\n\n// Initialize the content generator\nnew TwitterContentGenerator();\n"], "names": [], "sourceRoot": ""}