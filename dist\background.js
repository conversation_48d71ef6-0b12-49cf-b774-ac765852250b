(()=>{async function e(e,t){return new Promise((r,n)=>{setTimeout(()=>{"generate"===e?r(`Generated tweet about ${t.keywords} with ${t.tone} tone`):"predict"===e?r({likes:Math.floor(100*Math.random())+10,retweets:Math.floor(50*Math.random())+5,replies:Math.floor(30*Math.random())+2,engagementRate:(10*Math.random()+1).toFixed(1)}):n(new Error("Unknown API call type"))},1e3+2e3*Math.random())})}console.log("Twitter Content Generator: Background script loaded"),chrome.runtime.onInstalled.addListener(e=>{console.log("Twitter Content Generator installed:",e.reason),"install"===e.reason&&chrome.storage.sync.set({enabled:!0,apiKey:"",defaultTone:"professional",defaultLength:"medium"});try{chrome.contextMenus.create({id:"generateTweet",title:"生成推文",contexts:["selection"],documentUrlPatterns:["https://twitter.com/*","https://x.com/*"]})}catch(e){console.error("Error creating context menu:",e)}}),chrome.runtime.onMessage.addListener((t,r,n)=>{switch(console.log("Background received message:",t),t.action){case"loadGenerator":!function(e,t,r){console.log("Loading generator for container:",e.containerId),r({success:!0})}(t,0,n);break;case"saveSettings":!function(e,t){chrome.storage.sync.set(e.settings,()=>{chrome.runtime.lastError?t({success:!1,error:chrome.runtime.lastError.message}):t({success:!0})})}(t,n);break;case"getSettings":!function(e){chrome.storage.sync.get(["enabled","apiKey","defaultTone","defaultLength"],t=>{chrome.runtime.lastError?e({success:!1,error:chrome.runtime.lastError.message}):e({success:!0,settings:{enabled:!1!==t.enabled,apiKey:t.apiKey||"",defaultTone:t.defaultTone||"professional",defaultLength:t.defaultLength||"medium"}})})}(n);break;case"generateTweet":return async function(t,r){try{const n=await new Promise(e=>{chrome.storage.sync.get(["apiKey"],t=>{e(t)})});r({success:!0,data:await e("generate",{keywords:t.keywords,tone:t.tone,length:t.length,apiKey:n.apiKey})})}catch(e){console.error("Error generating tweet:",e),r({success:!1,error:e.message})}}(t,n),!0;case"predictEngagement":return async function(t,r){try{const n=await new Promise(e=>{chrome.storage.sync.get(["apiKey"],t=>{e(t)})});r({success:!0,data:await e("predict",{text:t.text,apiKey:n.apiKey})})}catch(e){console.error("Error predicting engagement:",e),r({success:!1,error:e.message})}}(t,n),!0;default:console.warn("Unknown action:",t.action)}}),chrome.action.onClicked.addListener(async e=>{try{e.url&&(e.url.includes("twitter.com")||e.url.includes("x.com"))?await chrome.scripting.executeScript({target:{tabId:e.id},files:["content.js"]}):chrome.notifications&&chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:"Twitter Content Generator",message:"此扩展只能在 Twitter 网站上使用"})}catch(e){console.error("Error handling action click:",e)}}),chrome.contextMenus.onClicked.addListener((e,t)=>{"generateTweet"===e.menuItemId&&e.selectionText&&t.id&&chrome.tabs.sendMessage(t.id,{action:"generateFromSelection",text:e.selectionText}).catch(e=>{console.error("Error sending message to content script:",e)})})})();