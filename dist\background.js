/*! For license information please see background.js.LICENSE.txt */
(()=>{function e(){var n,r,o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function i(e,o,c,a){var i=o&&o.prototype instanceof u?o:u,l=Object.create(i.prototype);return t(l,"_invoke",function(e,t,o){var c,a,i,u=0,l=o||[],f=!1,d={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(e,t){return c=e,a=0,i=n,d.n=t,s}};function p(e,t){for(a=e,i=t,r=0;!f&&u&&!o&&r<l.length;r++){var o,c=l[r],p=d.p,m=c[2];e>3?(o=m===t)&&(i=c[(a=c[4])?5:(a=3,3)],c[4]=c[5]=n):c[0]<=p&&((o=e<2&&p<c[1])?(a=0,d.v=t,d.n=c[1]):p<m&&(o=e<3||c[0]>t||t>m)&&(c[4]=e,c[5]=t,d.n=m,a=0))}if(o||e>1)return s;throw f=!0,t}return function(o,l,m){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,m),a=l,i=m;(r=a<2?n:i)||!f;){c||(a?a<3?(a>1&&(d.n=-1),p(a,i)):d.n=i:d.v=i);try{if(u=2,c){if(a||(o="next"),r=c[o]){if(!(r=r.call(c,i)))throw TypeError("iterator result is not an object");if(!r.done)return r;i=r.value,a<2&&(a=0)}else 1===a&&(r=c.return)&&r.call(c),a<2&&(i=TypeError("The iterator does not provide a '"+o+"' method"),a=1);c=n}else if((r=(f=d.n<0)?i:e.call(t,d))!==s)break}catch(e){c=n,a=1,i=e}finally{u=1}}return{value:r,done:f}}}(e,c,a),!0),l}var s={};function u(){}function l(){}function f(){}r=Object.getPrototypeOf;var d=[][c]?r(r([][c]())):(t(r={},c,function(){return this}),r),p=f.prototype=u.prototype=Object.create(d);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,t(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=f,t(p,"constructor",f),t(f,"constructor",l),l.displayName="GeneratorFunction",t(f,a,"GeneratorFunction"),t(p),t(p,a,"Generator"),t(p,c,function(){return this}),t(p,"toString",function(){return"[object Generator]"}),(e=function(){return{w:i,m}})()}function t(e,n,r,o){var c=Object.defineProperty;try{c({},"",{})}catch(e){c=0}t=function(e,n,r,o){if(n)c?c(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var a=function(n,r){t(e,n,function(e){return this._invoke(n,r,e)})};a("next",0),a("throw",1),a("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,c,a){try{var i=e[c](a),s=i.value}catch(e){return void n(e)}i.done?t(s):Promise.resolve(s).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise(function(o,c){var a=e.apply(t,r);function i(e){n(a,o,c,i,s,"next",e)}function s(e){n(a,o,c,i,s,"throw",e)}i(void 0)})}}function o(){return(o=r(e().m(function t(n,r){var o,c,i;return e().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,new Promise(function(e){chrome.storage.sync.get(["apiKey"],function(t){e(t)})});case 1:return o=e.v,e.n=2,a("generate",{keywords:n.keywords,tone:n.tone,length:n.length,apiKey:o.apiKey});case 2:c=e.v,r({success:!0,data:c}),e.n=4;break;case 3:e.p=3,i=e.v,console.error("Error generating tweet:",i),r({success:!1,error:i.message});case 4:return e.a(2)}},t,null,[[0,3]])}))).apply(this,arguments)}function c(){return(c=r(e().m(function t(n,r){var o,c,i;return e().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,new Promise(function(e){chrome.storage.sync.get(["apiKey"],function(t){e(t)})});case 1:return o=e.v,e.n=2,a("predict",{text:n.text,apiKey:o.apiKey});case 2:c=e.v,r({success:!0,data:c}),e.n=4;break;case 3:e.p=3,i=e.v,console.error("Error predicting engagement:",i),r({success:!1,error:i.message});case 4:return e.a(2)}},t,null,[[0,3]])}))).apply(this,arguments)}function a(e,t){return i.apply(this,arguments)}function i(){return(i=r(e().m(function t(n,r){return e().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e,t){setTimeout(function(){"generate"===n?e("Generated tweet about ".concat(r.keywords," with ").concat(r.tone," tone")):"predict"===n?e({likes:Math.floor(100*Math.random())+10,retweets:Math.floor(50*Math.random())+5,replies:Math.floor(30*Math.random())+2,engagementRate:(10*Math.random()+1).toFixed(1)}):t(new Error("Unknown API call type"))},1e3+2e3*Math.random())}))},t)}))).apply(this,arguments)}console.log("Twitter Content Generator: Background script loaded"),chrome.runtime.onInstalled.addListener(function(e){console.log("Twitter Content Generator installed:",e.reason),"install"===e.reason&&chrome.storage.sync.set({enabled:!0,apiKey:"",defaultTone:"professional",defaultLength:"medium"})}),chrome.runtime.onMessage.addListener(function(e,t,n){switch(console.log("Background received message:",e),e.action){case"loadGenerator":!function(e,t,n){console.log("Loading generator for container:",e.containerId),n({success:!0})}(e,0,n);break;case"saveSettings":!function(e,t){chrome.storage.sync.set(e.settings,function(){chrome.runtime.lastError?t({success:!1,error:chrome.runtime.lastError.message}):t({success:!0})})}(e,n);break;case"getSettings":!function(e){chrome.storage.sync.get(["enabled","apiKey","defaultTone","defaultLength"],function(t){chrome.runtime.lastError?e({success:!1,error:chrome.runtime.lastError.message}):e({success:!0,settings:{enabled:!1!==t.enabled,apiKey:t.apiKey||"",defaultTone:t.defaultTone||"professional",defaultLength:t.defaultLength||"medium"}})})}(n);break;case"generateTweet":return function(e,t){o.apply(this,arguments)}(e,n),!0;case"predictEngagement":return function(e,t){c.apply(this,arguments)}(e,n),!0;default:console.warn("Unknown action:",e.action)}}),chrome.action.onClicked.addListener(function(e){e.url.includes("twitter.com")||e.url.includes("x.com")?chrome.scripting.executeScript({target:{tabId:e.id},files:["content.js"]}):chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:"Twitter Content Generator",message:"此扩展只能在 Twitter 网站上使用"})}),chrome.contextMenus.create({id:"generateTweet",title:"生成推文",contexts:["selection"],documentUrlPatterns:["https://twitter.com/*","https://x.com/*"]}),chrome.contextMenus.onClicked.addListener(function(e,t){"generateTweet"===e.menuItemId&&e.selectionText&&chrome.tabs.sendMessage(t.id,{action:"generateFromSelection",text:e.selectionText})})})();