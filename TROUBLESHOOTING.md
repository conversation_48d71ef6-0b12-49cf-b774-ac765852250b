# Twitter Content Generator - 故障排除指南

## 🚨 Service Worker 注册失败问题

### 错误代码 15 - 解决方案

如果您遇到 "Service worker registration failed. Status code: 15" 错误，请按以下步骤操作：

#### 1. 检查Chrome版本
确保您使用的是Chrome 88或更高版本：
- 在地址栏输入 `chrome://version/`
- 检查版本号

#### 2. 清理扩展缓存
1. 打开 `chrome://extensions/`
2. 找到 Twitter Content Generator 扩展
3. 点击"移除"按钮
4. 重新加载扩展

#### 3. 检查文件完整性
确保以下文件存在于 `dist` 文件夹中：
- ✅ `manifest.json`
- ✅ `background.js`
- ✅ `content.js`
- ✅ `popup.html`
- ✅ `popup.js`
- ✅ `injected.js`
- ✅ `content.css`
- ✅ `icons/` 文件夹及图标文件

#### 4. 验证manifest.json
检查 `dist/manifest.json` 文件内容：

```json
{
  "manifest_version": 3,
  "name": "Twitter Content Generator",
  "version": "1.0.0",
  "permissions": [
    "activeTab",
    "storage",
    "scripting",
    "contextMenus",
    "notifications"
  ],
  "background": {
    "service_worker": "background.js"
  }
}
```

#### 5. 重新构建项目
```bash
# 清理构建文件
npm run clean

# 重新安装依赖
npm install

# 重新构建
npm run build
```

#### 6. 开发者工具调试
1. 在 `chrome://extensions/` 页面
2. 启用"开发者模式"
3. 加载扩展后，点击"检查视图 service worker"
4. 查看控制台错误信息

### 常见错误及解决方案

#### 错误：语法错误
**症状**：Service Worker无法启动，控制台显示语法错误
**解决方案**：
1. 检查 `background.js` 文件语法
2. 确保所有函数都正确定义
3. 检查是否有未闭合的括号或引号

#### 错误：权限不足
**症状**：某些功能无法使用，如通知或上下文菜单
**解决方案**：
1. 确保 `manifest.json` 包含所需权限
2. 重新加载扩展以应用新权限

#### 错误：文件路径问题
**症状**：无法加载资源文件
**解决方案**：
1. 检查 `webpack.config.js` 配置
2. 确保所有资源文件都被正确复制到 `dist` 文件夹

## 🔧 其他常见问题

### 问题：扩展按钮不显示
**可能原因**：
- 不在Twitter网站上
- 内容脚本未正确注入
- Twitter页面结构发生变化

**解决方案**：
1. 确保在 `twitter.com` 或 `x.com` 上
2. 刷新页面
3. 检查浏览器控制台错误

### 问题：生成功能不工作
**可能原因**：
- API配置问题
- 网络连接问题
- 模拟数据生成失败

**解决方案**：
1. 检查网络连接
2. 查看控制台错误信息
3. 验证API配置

### 问题：推文无法插入
**可能原因**：
- Twitter页面DOM结构变化
- 权限不足
- 推文框未激活

**解决方案**：
1. 手动点击推文编辑框
2. 检查内容脚本是否正确运行
3. 使用复制到剪贴板功能作为备选

## 🛠️ 开发者调试步骤

### 1. 启用详细日志
在 `background.js` 开头添加：
```javascript
console.log('Background script starting...');
```

### 2. 检查扩展状态
```javascript
// 在控制台运行
chrome.runtime.getManifest()
```

### 3. 测试权限
```javascript
// 检查权限
chrome.permissions.getAll((permissions) => {
  console.log('Current permissions:', permissions);
});
```

### 4. 监听错误
```javascript
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension startup');
});

chrome.runtime.onSuspend.addListener(() => {
  console.log('Extension suspend');
});
```

## 📞 获取帮助

如果以上解决方案都无法解决问题：

1. **收集错误信息**：
   - 截图错误消息
   - 复制控制台错误日志
   - 记录操作步骤

2. **检查环境**：
   - Chrome版本
   - 操作系统
   - 扩展版本

3. **提交问题报告**：
   - 包含详细的错误信息
   - 提供重现步骤
   - 附上相关截图

## 🔄 重置扩展

如果问题持续存在，可以完全重置扩展：

1. **移除扩展**：
   ```
   chrome://extensions/ → 移除扩展
   ```

2. **清理数据**：
   ```
   chrome://settings/content/all → 搜索twitter.com → 清除数据
   ```

3. **重新安装**：
   ```bash
   npm run clean
   npm run build
   # 重新加载扩展
   ```

---

**记住**：大多数Service Worker问题都可以通过重新构建和重新加载扩展来解决。如果问题持续存在，请检查Chrome开发者工具中的详细错误信息。
