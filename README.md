# Twitter Content Generator Chrome Extension

一个基于AI的Twitter内容生成和优化Chrome浏览器扩展，帮助用户创建高质量的推文内容并预测互动率。

## 功能特点

- 🤖 **AI推文生成**: 基于关键词智能生成推文内容
- 🎯 **多种语调**: 支持专业、轻松、幽默、励志、争议等多种语调风格
- 📊 **互动率预测**: 预测推文的点赞、转发、回复数量和互动率
- 🎨 **无缝集成**: 直接在Twitter网页上添加生成按钮
- ⚡ **一键使用**: 生成的内容可一键插入到推文编辑框
- 🔧 **可定制**: 支持自定义API配置和参数设置

## 安装方法

### 开发环境安装

1. 克隆或下载项目到本地
2. 安装依赖：
   ```bash
   npm install
   ```
3. 构建项目：
   ```bash
   npm run build
   ```
4. 在Chrome浏览器中加载扩展：
   - 打开 `chrome://extensions/`
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目的 `dist` 文件夹

### 生产环境安装

1. 下载发布版本的 `.crx` 文件
2. 在Chrome中打开 `chrome://extensions/`
3. 将 `.crx` 文件拖拽到页面中安装

## 使用方法

1. **访问Twitter**: 在浏览器中打开 [twitter.com](https://twitter.com) 或 [x.com](https://x.com)
2. **开始写推文**: 点击"发推文"按钮或回复推文
3. **使用AI生成**: 在推文编辑区域会出现"AI生成"按钮
4. **输入关键词**: 点击按钮后输入要讨论的主题或关键词
5. **选择参数**: 选择语调风格和推文长度
6. **生成内容**: 点击"生成推文"获取AI建议
7. **查看预测**: 查看每条推文的互动率预测
8. **使用推文**: 选择喜欢的推文内容一键插入

## 项目结构

```
twittergo/
├── manifest.json              # Chrome扩展清单文件
├── package.json              # Node.js项目配置
├── webpack.config.js         # Webpack构建配置
├── .babelrc                  # Babel转译配置
├── src/                      # 源代码目录
│   ├── content/             # 内容脚本
│   │   └── content.js       # 主要内容脚本
│   ├── background/          # 后台脚本
│   │   └── background.js    # 后台服务脚本
│   ├── popup/               # 弹出窗口
│   │   ├── popup.html       # 弹出窗口HTML
│   │   └── popup.js         # 弹出窗口脚本
│   ├── injected/            # 注入脚本
│   │   └── injected.js      # React组件注入脚本
│   ├── components/          # React组件
│   │   └── TwitterGenerator.jsx # 主要生成器组件
│   ├── services/            # 服务模块
│   │   └── apiService.js    # API调用服务
│   ├── styles/              # 样式文件
│   │   └── content.css      # 内容脚本样式
│   └── icons/               # 图标文件
└── dist/                    # 构建输出目录
```

## API配置

### 使用自定义API

1. 修改 `src/services/apiService.js` 中的API配置：
   ```javascript
   const API_CONFIG = {
     TWEET_GENERATION_URL: 'https://your-api.com/generate-tweet',
     ENGAGEMENT_PREDICTION_URL: 'https://your-api.com/predict-engagement',
     API_KEY: 'your-api-key-here'
   };
   ```

2. 确保API端点支持以下请求格式：

   **推文生成API**:
   ```json
   POST /generate-tweet
   {
     "keywords": "关键词",
     "tone": "professional|casual|humorous|inspirational|controversial",
     "length": "short|medium|long",
     "platform": "twitter",
     "max_length": 280
   }
   ```

   **互动率预测API**:
   ```json
   POST /predict-engagement
   {
     "text": "推文内容",
     "platform": "twitter"
   }
   ```

## 开发命令

- `npm run dev`: 开发模式构建（监听文件变化）
- `npm run build`: 生产模式构建
- `npm run clean`: 清理构建文件

## 技术栈

- **前端框架**: React 18
- **构建工具**: Webpack 5
- **转译器**: Babel
- **样式**: CSS3
- **API请求**: Fetch API
- **存储**: Chrome Storage API

## 浏览器兼容性

- Chrome 88+
- Edge 88+
- 其他基于Chromium的浏览器

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持AI推文生成
- 支持互动率预测
- 支持多种语调风格
- 支持Chrome扩展基础功能
