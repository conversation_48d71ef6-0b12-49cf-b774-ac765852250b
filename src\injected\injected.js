// This script will be injected directly into the page
// It creates the modal UI without using React modules

// Global app object for the injected script
window.TwitterGeneratorApp = {
  currentModal: null,

  init(containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('Container not found:', containerId);
      return;
    }

    // Create the generator UI directly with HTML
    this.createGeneratorUI(container);
    this.currentModal = container;
  },

  createGeneratorUI(container) {
    container.innerHTML = `
      <div class="generator-modal-content">
        <div class="generator-modal-header">
          <h2 class="generator-modal-title">AI推文生成器</h2>
          <button class="generator-close-btn" onclick="window.TwitterGeneratorApp.close()">
            ×
          </button>
        </div>

        <form class="generator-form" onsubmit="window.TwitterGeneratorApp.handleGenerate(event)">
          <div class="generator-input-group">
            <label class="generator-label" for="keywords">
              关键词 *
            </label>
            <input
              id="keywords"
              type="text"
              class="generator-input"
              placeholder="输入要讨论的主题或关键词..."
              required
            />
          </div>

          <div class="generator-input-group">
            <label class="generator-label" for="tone">
              语调风格
            </label>
            <select id="tone" class="generator-input">
              <option value="professional">专业</option>
              <option value="casual">轻松</option>
              <option value="humorous">幽默</option>
              <option value="inspirational">励志</option>
              <option value="controversial">争议</option>
            </select>
          </div>

          <div class="generator-input-group">
            <label class="generator-label" for="length">
              推文长度
            </label>
            <select id="length" class="generator-input">
              <option value="short">简短 (50-100字)</option>
              <option value="medium" selected>中等 (100-200字)</option>
              <option value="long">较长 (200-280字)</option>
            </select>
          </div>

          <button type="submit" class="generator-generate-btn" id="generateBtn">
            生成推文
          </button>
        </form>

        <div id="loadingDiv" class="generator-loading" style="display: none;">
          <div class="generator-spinner"></div>
          <span>正在生成推文...</span>
        </div>

        <div id="resultsDiv" class="generator-results" style="display: none;">
          <h3 style="margin-bottom: 16px; fontSize: 18px; font-weight: 600;">
            生成结果
          </h3>
          <div id="resultsContainer"></div>
        </div>
      </div>
    `;
  },

  close() {
    if (this.currentModal) {
      this.currentModal.remove();
      this.currentModal = null;
    }
  },

  async handleGenerate(event) {
    event.preventDefault();

    const keywords = document.getElementById('keywords').value;
    const tone = document.getElementById('tone').value;
    const length = document.getElementById('length').value;

    if (!keywords.trim()) {
      alert('请输入关键词');
      return;
    }

    // Show loading
    document.getElementById('loadingDiv').style.display = 'flex';
    document.getElementById('resultsDiv').style.display = 'none';
    document.getElementById('generateBtn').disabled = true;

    try {
      // Generate tweets
      const tweets = await this.generateTweets({ keywords, tone, length });

      // Show results
      this.displayResults(tweets);
    } catch (error) {
      console.error('Error generating tweets:', error);
      alert('生成推文时出错，请重试');
    } finally {
      // Hide loading
      document.getElementById('loadingDiv').style.display = 'none';
      document.getElementById('generateBtn').disabled = false;
    }
  },

  async generateTweets(params) {
    // Simulate API call for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const tones = {
          professional: '专业',
          casual: '轻松',
          humorous: '幽默',
          inspirational: '励志',
          controversial: '争议'
        };

        const tweets = [
          {
            text: `关于${params.keywords}的${tones[params.tone]}思考：这个话题值得我们深入探讨。每个人都有自己独特的见解，让我们一起分享和学习。#${params.keywords} #思考`,
            engagement: {
              likes: Math.floor(Math.random() * 100) + 20,
              retweets: Math.floor(Math.random() * 50) + 10,
              replies: Math.floor(Math.random() * 30) + 5,
              engagementRate: (Math.random() * 5 + 2).toFixed(1)
            }
          },
          {
            text: `${params.keywords}让我想到了很多。在这个快速变化的时代，我们需要保持开放的心态，不断学习和成长。你们怎么看？`,
            engagement: {
              likes: Math.floor(Math.random() * 80) + 15,
              retweets: Math.floor(Math.random() * 40) + 8,
              replies: Math.floor(Math.random() * 25) + 3,
              engagementRate: (Math.random() * 4 + 1.5).toFixed(1)
            }
          },
          {
            text: `今天想和大家聊聊${params.keywords}。这个话题很有意思，希望能听到更多不同的声音和观点。互动是最好的学习方式！`,
            engagement: {
              likes: Math.floor(Math.random() * 120) + 25,
              retweets: Math.floor(Math.random() * 60) + 12,
              replies: Math.floor(Math.random() * 35) + 8,
              engagementRate: (Math.random() * 6 + 2.5).toFixed(1)
            }
          }
        ];

        resolve(tweets);
      }, 1500);
    });
  },

  displayResults(tweets) {
    const container = document.getElementById('resultsContainer');
    container.innerHTML = '';

    tweets.forEach((tweet, index) => {
      const tweetDiv = document.createElement('div');
      tweetDiv.className = 'generator-tweet-result';
      tweetDiv.innerHTML = `
        <div class="generator-tweet-text">
          ${tweet.text}
        </div>
        <div class="generator-tweet-stats">
          <span>预测互动：</span>
          <span>👍 ${tweet.engagement.likes}</span>
          <span>🔄 ${tweet.engagement.retweets}</span>
          <span>💬 ${tweet.engagement.replies}</span>
          <span>📊 ${tweet.engagement.engagementRate}%</span>
        </div>
        <button class="generator-use-btn" onclick="window.TwitterGeneratorApp.useTweet('${tweet.text.replace(/'/g, "\\'")}')">
          使用这条推文
        </button>
      `;
      container.appendChild(tweetDiv);
    });

    document.getElementById('resultsDiv').style.display = 'block';
  },

  useTweet(tweetText) {
    // Find Twitter compose box and insert the generated text
    const selectors = [
      '[data-testid="tweetTextarea_0"]',
      '.DraftEditor-root .public-DraftEditor-content',
      '[role="textbox"][data-testid="tweetTextarea_0"]'
    ];

    let composeBox = null;
    for (const selector of selectors) {
      composeBox = document.querySelector(selector);
      if (composeBox) break;
    }

    if (composeBox) {
      // Clear existing content
      composeBox.focus();
      document.execCommand('selectAll');
      document.execCommand('delete');
      
      // Insert new text
      document.execCommand('insertText', false, tweetText);
      
      // Trigger input event to notify Twitter's React components
      const inputEvent = new Event('input', { bubbles: true });
      composeBox.dispatchEvent(inputEvent);
      
      console.log('Tweet text inserted successfully');
    } else {
      console.error('Could not find Twitter compose box');
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(tweetText).then(() => {
        alert('推文已复制到剪贴板，请手动粘贴到推文框中');
      });
    }
  }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Twitter Generator injected script loaded');
  });
} else {
  console.log('Twitter Generator injected script loaded');
}
