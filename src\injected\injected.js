import React from 'react';
import { createRoot } from 'react-dom/client';
import Twitter<PERSON>enerator from '../components/TwitterGenerator';

// Global app object for the injected script
window.TwitterGeneratorApp = {
  root: null,
  
  init(containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('Container not found:', containerId);
      return;
    }

    // Create React root
    this.root = createRoot(container);
    
    // Render the TwitterGenerator component
    this.root.render(
      React.createElement(TwitterGenerator, {
        onClose: () => this.close(containerId),
        onUseTweet: (tweetText) => this.useTweet(tweetText)
      })
    );
  },

  close(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
      container.remove();
    }
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
  },

  useTweet(tweetText) {
    // Find Twitter compose box and insert the generated text
    const selectors = [
      '[data-testid="tweetTextarea_0"]',
      '.DraftEditor-root .public-DraftEditor-content',
      '[role="textbox"][data-testid="tweetTextarea_0"]'
    ];

    let composeBox = null;
    for (const selector of selectors) {
      composeBox = document.querySelector(selector);
      if (composeBox) break;
    }

    if (composeBox) {
      // Clear existing content
      composeBox.focus();
      document.execCommand('selectAll');
      document.execCommand('delete');
      
      // Insert new text
      document.execCommand('insertText', false, tweetText);
      
      // Trigger input event to notify Twitter's React components
      const inputEvent = new Event('input', { bubbles: true });
      composeBox.dispatchEvent(inputEvent);
      
      console.log('Tweet text inserted successfully');
    } else {
      console.error('Could not find Twitter compose box');
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(tweetText).then(() => {
        alert('推文已复制到剪贴板，请手动粘贴到推文框中');
      });
    }
  }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Twitter Generator injected script loaded');
  });
} else {
  console.log('Twitter Generator injected script loaded');
}
