# 调试指南 - 模态框空白问题

## 🔧 最新修复

我已经进行了以下修复：

1. **改进了容器创建**：
   - 在modal中创建了专门的content容器
   - 添加了点击遮罩关闭功能

2. **增强了错误处理**：
   - 添加了详细的控制台日志
   - 添加了脚本加载失败的fallback界面
   - 添加了初始化超时处理

3. **改进了初始化逻辑**：
   - 添加了延迟初始化以确保脚本完全加载
   - 添加了更多调试信息

## 🧪 调试步骤

### 1. 重新加载扩展
```
1. 访问 chrome://extensions/
2. 找到 Twitter Content Generator
3. 点击刷新按钮 🔄
4. 确保没有错误提示
```

### 2. 打开开发者工具
```
1. 访问 https://twitter.com
2. 按 F12 打开开发者工具
3. 切换到 Console 标签页
4. 清空控制台日志
```

### 3. 测试并观察日志
```
1. 点击"发推文"按钮
2. 点击蓝色的"AI生成"按钮
3. 观察控制台输出，应该看到：
   - "Loading component for container: twitter-generator-content"
   - "Twitter Generator injected script loaded"
   - "TwitterGeneratorApp available: true"
   - "Injected script loaded, checking TwitterGeneratorApp..."
   - "TwitterGeneratorApp found, initializing..."
```

### 4. 检查DOM结构
```
1. 在开发者工具中切换到 Elements 标签页
2. 查找 <div id="twitter-generator-modal">
3. 应该包含：
   - <div id="twitter-generator-content">
   - 内部应该有完整的表单HTML
```

## 🔍 可能的错误情况

### 情况1：脚本加载失败
**控制台显示**：`Failed to load injected script`
**解决方案**：
```
1. 检查 dist/injected.js 文件是否存在
2. 重新构建项目：npm run build
3. 重新加载扩展
```

### 情况2：TwitterGeneratorApp未定义
**控制台显示**：`TwitterGeneratorApp not found!`
**解决方案**：
```
1. 检查 injected.js 是否正确构建
2. 查看是否有JavaScript语法错误
3. 尝试手动在控制台输入：window.TwitterGeneratorApp
```

### 情况3：容器未找到
**控制台显示**：`Container not found: twitter-generator-content`
**解决方案**：
```
1. 检查content script是否正确创建了容器
2. 在Elements标签页查找容器元素
3. 可能需要刷新页面重试
```

## 🛠️ 手动测试方法

如果自动初始化失败，可以手动测试：

### 1. 在控制台手动初始化
```javascript
// 检查对象是否存在
console.log(window.TwitterGeneratorApp);

// 手动创建容器并初始化
const container = document.createElement('div');
container.id = 'test-container';
container.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);z-index:10000;background:rgba(0,0,0,0.5);width:100%;height:100%;display:flex;align-items:center;justify-content:center;';
document.body.appendChild(container);

// 初始化
window.TwitterGeneratorApp.init('test-container');
```

### 2. 检查CSS加载
```javascript
// 检查CSS是否加载
const styles = Array.from(document.styleSheets);
console.log('Loaded stylesheets:', styles.length);

// 检查特定样式
const hasGeneratorStyles = Array.from(document.styleSheets).some(sheet => {
  try {
    return Array.from(sheet.cssRules).some(rule => 
      rule.selectorText && rule.selectorText.includes('generator-modal-content')
    );
  } catch(e) { return false; }
});
console.log('Generator styles loaded:', hasGeneratorStyles);
```

## 📋 完整的调试检查清单

### ✅ 扩展状态检查
- [ ] 扩展已启用
- [ ] 没有错误图标
- [ ] 权限已授予

### ✅ 文件检查
- [ ] dist/manifest.json 存在
- [ ] dist/content.js 存在
- [ ] dist/injected.js 存在
- [ ] dist/content.css 存在

### ✅ 页面检查
- [ ] 在 twitter.com 或 x.com 上
- [ ] AI生成按钮显示
- [ ] 点击按钮有响应

### ✅ 控制台检查
- [ ] 没有红色错误信息
- [ ] 看到初始化日志
- [ ] TwitterGeneratorApp 对象存在

### ✅ DOM检查
- [ ] modal容器被创建
- [ ] content容器存在
- [ ] HTML内容被插入

## 🚨 如果仍然失败

请提供以下信息：

1. **控制台完整日志**（截图或复制文本）
2. **Elements标签页的DOM结构**（截图）
3. **Chrome版本**：在地址栏输入 `chrome://version/`
4. **扩展详情**：在 `chrome://extensions/` 中的扩展状态

## 🔄 重置方法

如果问题持续存在：

```bash
# 1. 完全重新构建
npm run clean  # 如果有这个命令
rm -rf dist/   # 删除构建文件
npm run build

# 2. 重新安装扩展
# 在 chrome://extensions/ 中：
# - 移除扩展
# - 重新加载扩展

# 3. 清除浏览器缓存
# chrome://settings/clearBrowserData
# 选择"缓存的图片和文件"
```

---

**记住**：每次修改代码后都需要重新构建（`npm run build`）并刷新扩展！
